# Automatización de Queue:Work para Importaciones

Este documento explica las diferentes opciones implementadas para ejecutar automáticamente `queue:work` después de las importaciones en el sistema.

## Opciones Implementadas

### 1. Callback `after` en ImportAction (Implementación Principal)

**Ubicación:** `app/Filament/Resources/DriverResource/Pages/ListDrivers.php`

Esta es la implementación principal que se ejecuta automáticamente después de cada importación:

```php
Actions\ImportAction::make()
    ->importer(DriverImporter::class)
    ->icon('heroicon-o-arrow-up-tray')
    ->after(function () {
        // Ejecutar comando personalizado para procesar la cola
        $exitCode = Artisan::call('import:process-queue', [
            '--continuous' => true,
            '--timeout' => 60,
            '--memory' => 512,
        ]);
        
        // Mostrar notificaciones según el resultado
    })
```

**Ventajas:**
- Se ejecuta automáticamente después de cada importación
- Proporciona feedback visual al usuario
- Usa un comando personalizado más robusto
- Maneja errores y muestra notificaciones apropiadas

### 2. Comando Personalizado `import:process-queue`

**Ubicación:** `app/Console/Commands/ProcessImportQueue.php`

Comando especializado para procesar colas de importación:

```bash
# Procesar un solo trabajo
php artisan import:process-queue

# Procesar todos los trabajos pendientes
php artisan import:process-queue --continuous

# Con opciones personalizadas
php artisan import:process-queue --continuous --timeout=120 --memory=1024 --tries=5
```

**Características:**
- Modo continuo para procesar todos los trabajos pendientes
- Configuración flexible de timeout, memoria y reintentos
- Verificación inteligente de trabajos pendientes
- Logging detallado del progreso
- Manejo robusto de errores

### 3. Job Personalizado `ProcessQueueAfterImport`

**Ubicación:** `app/Jobs/ProcessQueueAfterImport.php`

Job que puede ser despachado para procesar la cola de forma asíncrona:

```php
ProcessQueueAfterImport::dispatch()
    ->delay(now()->addSeconds(2));
```

**Características:**
- Ejecución asíncrona
- Configuración de timeout y reintentos
- Cola separada para evitar conflictos
- Logging completo de actividades

## Configuración del Sistema

### Variables de Entorno Recomendadas

```env
QUEUE_CONNECTION=database
DB_QUEUE_TABLE=jobs
DB_QUEUE=default
DB_QUEUE_RETRY_AFTER=90
```

### Migraciones Requeridas

Asegúrate de que las siguientes migraciones estén ejecutadas:

```bash
# Laravel 11+
php artisan make:queue-batches-table
php artisan make:notifications-table

# Laravel 10
php artisan queue:batches-table
php artisan notifications:table

# Filament
php artisan vendor:publish --tag=filament-actions-migrations
php artisan migrate
```

## Uso Manual

### Comando de Desarrollo

Para desarrollo, puedes usar el script `dev` en `composer.json`:

```bash
composer run dev
```

Esto ejecuta automáticamente:
- `php artisan serve`
- `php artisan queue:listen --tries=1`
- `php artisan pail --timeout=0`
- `npm run dev`

### Comandos Manuales

```bash
# Procesar cola manualmente
php artisan queue:work --once

# Procesar con nuestro comando personalizado
php artisan import:process-queue --continuous

# Ver trabajos pendientes
php artisan queue:monitor

# Limpiar trabajos fallidos
php artisan queue:clear
```

## Monitoreo y Debugging

### Logs

Los logs se guardan en:
- `storage/logs/laravel.log` - Logs generales
- Notificaciones en la interfaz de Filament

### Verificar Estado de la Cola

```php
// En código PHP
$pendingJobs = DB::table('jobs')->count();

// En comando artisan
php artisan queue:monitor
```

## Consideraciones de Producción

### Supervisor (Recomendado para Producción)

Para producción, es recomendable usar Supervisor para mantener workers corriendo:

```ini
[program:sistemamina-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/sistemamina/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/path/to/sistemamina/storage/logs/worker.log
stopwaitsecs=3600
```

### Alternativas para Hosting Compartido

Si no puedes usar Supervisor, puedes:

1. **Usar cron jobs:**
```bash
* * * * * cd /path/to/sistemamina && php artisan schedule:run >> /dev/null 2>&1
```

2. **Configurar el sistema actual** que ejecuta automáticamente después de cada importación.

## Troubleshooting

### Problemas Comunes

1. **Trabajos no se procesan:**
   - Verificar que `QUEUE_CONNECTION=database`
   - Ejecutar migraciones de cola
   - Verificar permisos de base de datos

2. **Timeout en importaciones grandes:**
   - Aumentar `--timeout` en el comando
   - Aumentar `--memory` si es necesario
   - Considerar reducir `chunkSize` en el ImportAction

3. **Memoria insuficiente:**
   - Aumentar `memory_limit` en PHP
   - Usar `--memory` en el comando
   - Procesar en chunks más pequeños

### Debug

```bash
# Ver trabajos en cola
php artisan queue:monitor

# Procesar con verbose
php artisan queue:work --verbose

# Ver logs en tiempo real
tail -f storage/logs/laravel.log
```
