<?php

namespace App\Filament\Resources\DriverResource\Pages;

use Filament\Actions;
use App\Imports\DriverImport;
use App\Filament\Imports\DriverImporter;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Resources\DriverResource;

class ListDrivers extends ListRecords
{
    protected static string $resource = DriverResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ImportAction::make()
                ->importer(DriverImporter::class)
                ->icon('heroicon-o-arrow-up-tray'),
            Actions\CreateAction::make()
                ->icon('heroicon-o-squares-plus'),

        ];
    }
}
