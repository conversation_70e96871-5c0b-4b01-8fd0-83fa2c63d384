<?php

namespace App\Filament\Resources\DriverResource\Pages;

use Filament\Actions;
use App\Imports\DriverImport;
use App\Filament\Imports\DriverImporter;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Resources\DriverResource;
use Illuminate\Support\Facades\Artisan;

class ListDrivers extends ListRecords
{
    protected static string $resource = DriverResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ImportAction::make()
                ->importer(DriverImporter::class)
                ->icon('heroicon-o-arrow-up-tray')
                ->after(function () {
                    // Ejecutar nuestro comando personalizado para procesar la cola
                    try {
                        $exitCode = Artisan::call('import:process-queue', [
                            '--continuous' => true,
                            '--timeout' => 60,
                            '--memory' => 512,
                        ]);

                        if ($exitCode === 0) {
                            // Mostrar notificación de éxito
                            \Filament\Notifications\Notification::make()
                                ->title('Cola procesada automáticamente')
                                ->body('Todos los trabajos de importación han sido procesados exitosamente.')
                                ->success()
                                ->send();
                        } else {
                            // Mostrar notificación de advertencia
                            \Filament\Notifications\Notification::make()
                                ->title('Procesamiento de cola completado con advertencias')
                                ->body('El procesamiento terminó, pero algunos trabajos pueden haber fallado.')
                                ->warning()
                                ->send();
                        }
                    } catch (\Exception $e) {
                        // Mostrar notificación de error
                        \Filament\Notifications\Notification::make()
                            ->title('Error al procesar cola')
                            ->body('Hubo un error al procesar automáticamente los trabajos: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
            Actions\CreateAction::make()
                ->icon('heroicon-o-squares-plus'),

        ];
    }
}
